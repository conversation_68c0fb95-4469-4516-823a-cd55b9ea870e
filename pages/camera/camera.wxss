/* pages/camera/camera.wxss */
.container {
  position: relative;
  width: 100vw;
  height: 100vh;
  background-color: #000;
  display: flex;
  flex-direction: column;
}

/* 导航栏样式 */
.camera-nav {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
}

/* 顶部提示样式 */
.header {
  padding: 40rpx 30rpx;
  text-align: center;
  background: rgba(0, 0, 0, 0.5);
  position: fixed;
  top: calc(180rpx + env(safe-area-inset-top));
  left: 0;
  right: 0;
  z-index: 10;
}

.title {
  font-size: 36rpx;
  color: #FFFFFF;
  font-weight: bold;
  margin-bottom: 8rpx;
  display: block;
}

.subtitle {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  display: block;
}

/* 开发环境提示 */
.dev-tools-tip {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  z-index: 10;
  background: rgba(0, 0, 0, 0.8);
  padding: 20px;
  border-radius: 12px;
  width: 80%;
}

.tip-text {
  color: #fff;
  font-size: 16px;
  display: block;
  margin-bottom: 8px;
}

.tip-subtext {
  color: #999;
  font-size: 14px;
  display: block;
}

/* 相机容器 */
.camera-container {
  flex: 1;
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.camera {
  width: 100%;
  height: 100%;
}

/* 取景框样式 */
.viewfinder {
  position: absolute;
  left: 50%;
  /* 顶部距离为navbar高度100rpx+16rpx间距 */
  top: calc(100rpx + env(safe-area-inset-top, 0) + 16rpx);
  transform: translateX(-50%);
  width: 100%;
  height: 40%;
  border: 2px solid rgba(255, 255, 255, 0.5);
  box-sizing: border-box;
}

.corner {
  position: absolute;
  width: 20px;
  height: 20px;
  border-color: #fff;
  border-style: solid;
  border-width: 0;
}

.top-left {
  top: -2px;
  left: -2px;
  border-top-width: 4px;
  border-left-width: 4px;
}

.top-right {
  top: -2px;
  right: -2px;
  border-top-width: 4px;
  border-right-width: 4px;
}

.bottom-left {
  bottom: -2px;
  left: -2px;
  border-bottom-width: 4px;
  border-left-width: 4px;
}

.bottom-right {
  bottom: -2px;
  right: -2px;
  border-bottom-width: 4px;
  border-right-width: 4px;
}

/* 照片预览区域 */
.photos-preview {
  position: absolute;
  bottom: 200px;
  left: 0;
  width: 100%;
  height: 90px;
  background: rgba(0, 0, 0, 0.5);
  padding: 4px 0 0 0;
}

.photos-scroll {
  width: 100%;
  height: 100%;
  white-space: nowrap;
}

.photos-list {
  display: inline-flex;
  padding: 0 10px;
  height: 100%;
}

.photo-item {
  position: relative;
  width: 80px;
  height: 80px;
  margin-right: 10px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
}

.photo-item image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.photo-delete {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 24px;
  height: 24px;
  background: rgba(0, 0, 0, 0.6);
  color: #fff;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: bold;
}

/* 底部操作区 */
.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 120px;
  background: linear-gradient(180deg, #181f27 0%, #232b36 100%);
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 0 20px;
  box-sizing: border-box;
  z-index: 100;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #fff;
}

.action-btn.disabled {
  opacity: 0.5;
  pointer-events: none;
}

.action-icon {
  font-size: 24px;
  margin-bottom: 4px;
}

.action-text {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
}

/* 拍照按钮 */
.capture-btn {
  width: 80px;
  height: 80px;
  border-radius: 40px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 16px rgba(0,0,0,0.10);
  transition: all 0.2s ease;
}

.capture-btn-inner {
  width: 64px;
  height: 64px;
  border-radius: 32px;
  background: linear-gradient(135deg, #ff7e2d 0%, #ff3d2d 100%);
  transition: all 0.2s ease;
}

.capture-btn.pressed {
  transform: scale(0.95);
  background: #fff;
}

.capture-btn.pressed .capture-btn-inner {
  width: 56px;
  height: 56px;
  border-radius: 28px;
}

/* 上传进度条 */
.upload-progress {
  position: fixed;
  bottom: 120px;
  left: 0;
  width: 100%;
  padding: 10px 20px;
  background: rgba(0, 0, 0, 0.8);
  box-sizing: border-box;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-inner {
  height: 100%;
  background: #07c160;
  transition: width 0.3s ease;
}

.progress-text {
  color: #fff;
  font-size: 12px;
  text-align: center;
  display: block;
}

/* 无权限提示 */
.no-auth-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  z-index: 10;
}

.no-auth-text {
  color: #fff;
  font-size: 16px;
  margin-bottom: 20px;
  display: block;
}

.auth-btn {
  background: #07c160;
  color: #fff;
  border: none;
  padding: 10px 20px;
  border-radius: 20px;
  font-size: 14px;
}

.auth-btn.pressed {
  opacity: 0.8;
}

/* 识别结果弹窗样式 */
.result-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 100;
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.result-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  width: 90%;
  max-height: 80vh;
  background: #FFFFFF;
  border-radius: 24rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 2rpx solid var(--border-color);
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-color);
}

.close-btn {
  font-size: 40rpx;
  color: var(--text-color-secondary);
  padding: 10rpx;
}

/* 食材列表样式 */
.ingredients-list {
  flex: 1;
  padding: 20rpx;
  max-height: 60vh;
}

.ingredient-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-bottom: 2rpx solid var(--border-color);
}

.ingredient-image {
  width: 100rpx;
  height: 100rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
}

.ingredient-info {
  flex: 1;
}

.ingredient-name {
  font-size: 28rpx;
  color: var(--text-color);
  margin-bottom: 8rpx;
  display: block;
}

.ingredient-confidence {
  font-size: 24rpx;
  color: var(--text-color-secondary);
}

.ingredient-actions {
  display: flex;
  gap: 20rpx;
}

.action-icon {
  width: 40rpx;
  height: 40rpx;
  padding: 10rpx;
}

.action-icon image {
  width: 100%;
  height: 100%;
}

/* 底部按钮样式 */
.modal-footer {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  border-top: 2rpx solid var(--border-color);
}

.modal-footer .btn {
  flex: 1;
  margin: 0 10rpx;
}

.modal-footer .btn:first-child {
  margin-left: 0;
}

.modal-footer .btn:last-child {
  margin-right: 0;
}

/* 加载提示样式 */
.loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  z-index: 200;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-content {
  background: rgba(255, 255, 255, 0.9);
  padding: 40rpx;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 20rpx;
  animation: rotate 1s linear infinite;
}

.loading-text {
  font-size: 28rpx;
  color: var(--text-color);
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 顶部间距 */
.header-spacer {
  height: 120rpx;
}