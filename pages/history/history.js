Page({
  data: {
    historyList: []
  },

  onLoad() {
    // 模拟从本地存储或后端获取历史记录数据
    const mockHistory = [
      { id: 1, time: "2023-10-01 12:00", name: "胡萝卜", confidence: 100 },
      { id: 2, time: "2023-10-02 13:00", name: "白菜", confidence: 100 },
      { id: 3, time: "2023-10-03 14:00", name: "土豆", confidence: 100 }
    ];
    this.setData({ historyList: mockHistory });
  },

  viewDetail(e) {
    const id = e.currentTarget.dataset.id;
    console.log('点击历史记录，ID:', id);

    if (!id) {
      wx.showToast({
        title: '数据错误',
        icon: 'none'
      });
      return;
    }

    const url = "/pages/recipe-detail/recipe-detail?id=" + id;
    console.log('跳转URL:', url);

    wx.navigateTo({
      url: url,
      fail: (error) => {
        console.error('页面跳转失败:', error);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  }
}); 